"""
Kafka message processor service.
Handles incoming messages from Kafka topics and processes them.
"""
import logging
import asyncio
from typing import Dict, Any, List

from core.dependencies import Dependencies
from models.data_models import BuildingData, AddressData, MessageType, KafkaMessage, UseCaseMessage, GraphMetadataItem, Certificate
from services.ttl_converter import TTLConverter
from agents.rag_agent import RAGAgent


logger = logging.getLogger(__name__)


class KafkaProcessor:
    """Service for processing Kafka messages."""
    
    def __init__(self, dependencies: Dependencies):
        self.deps = dependencies
        self.ttl_converter = TTLConverter()
        self.rag_agent = RAGAgent(dependencies)
        self._running = False

    async def start(self):
        """Start the Kafka processor."""
        logger.info("Starting Kafka processor...")

        # Register message handler for usecases topics
        self.deps.kafka_client.register_handler("usecases", self._handle_usecase_message)

        # Get the configured topic pattern from settings
        usecase_pattern = self.deps.settings.kafka.usecase_topic_pattern

        # Convert shell-style wildcard to regex pattern
        # "data.usecases.*" becomes "^data\.usecases\..*$"
        # "usecases.*" becomes "^usecases\..*$"
        regex_pattern = f"^{usecase_pattern.replace('.', r'\.').replace('*', '.*')}$"

        logger.info(f"Using topic pattern: {usecase_pattern} -> regex: {regex_pattern}")

        # Subscribe to usecases topics using regex pattern
        await self.deps.kafka_client.subscribe_to_pattern(regex_pattern)

        # Start consuming
        self._running = True
        await self.deps.kafka_client.start_consuming()

    async def stop(self):
        """Stop the Kafka processor."""
        logger.info("Stopping Kafka processor...")
        self._running = False

    async def _handle_usecase_message(self, topic: str, message: Dict[str, Any]):
        """Handle usecase data messages from the new format."""
        try:
            logger.info(f"Processing usecase message from topic {topic}")

            # Parse the usecase message
            usecase_message = UseCaseMessage(**message)
            usecase_name = usecase_message.useCase

            # Process graph data if available
            if usecase_message.graphData:
                logger.info(f"Processing graph data for usecase: {usecase_name}")
                await self._process_graph_data(usecase_name, usecase_message.graphData)

            # Process graph template if available
            if usecase_message.graphTemplate:
                logger.info(f"Processing graph template for usecase: {usecase_name}")
                await self._process_graph_template(usecase_name, usecase_message.graphTemplate)

            # Process metadata items
            if usecase_message.graphMetadata:
                logger.info(f"Processing {len(usecase_message.graphMetadata)} metadata items for usecase: {usecase_name}")
                for metadata_item in usecase_message.graphMetadata:
                    await self._process_metadata_item(usecase_name, metadata_item)

        except Exception as e:
            logger.error(f"Error handling usecase message: {e}")

    async def _process_graph_data(self, usecase_name: str, graph_data: str):
        """Process graph data (TTL format) and upload to GraphDB."""
        try:
            # Upload TTL data directly to GraphDB
            if self.deps.graphdb_client:
                success = await self.deps.graphdb_client.upload_ttl_data(
                    ttl_content=graph_data,
                    repository=usecase_name.lower()
                )
                if success:
                    logger.info(f"Successfully uploaded graph data to GraphDB for usecase: {usecase_name}")
                else:
                    logger.error(f"Failed to upload graph data to GraphDB for usecase: {usecase_name}")
            else:
                logger.warning("GraphDB client not available")
        except Exception as e:
            logger.error(f"Error processing graph data: {e}")

    async def _process_graph_template(self, usecase_name: str, graph_template: str):
        """Process graph template and convert to TTL if needed."""
        try:
            # For now, just log the template - could be used for validation or transformation
            logger.info(f"Graph template for {usecase_name}: {graph_template[:100]}...")

            # Could potentially convert template to TTL and upload
            # This depends on the specific format of the template

        except Exception as e:
            logger.error(f"Error processing graph template: {e}")

    async def _process_metadata_item(self, usecase_name: str, metadata_item: GraphMetadataItem):
        """Process individual metadata items and handle certificates."""
        try:
            logger.info(f"Processing metadata item {metadata_item.id} of type {metadata_item.classType}")

            # Convert metadata to TTL
            ttl_result = await self.ttl_converter.convert_metadata_to_ttl(usecase_name, metadata_item)

            if ttl_result.success and self.deps.graphdb_client:
                # Upload to GraphDB
                success = await self.deps.graphdb_client.upload_ttl_data(
                    ttl_content=ttl_result.ttl_content,
                    repository=usecase_name.lower()
                )
                if success:
                    logger.info(f"Successfully uploaded metadata TTL for item {metadata_item.id}")
                else:
                    logger.error(f"Failed to upload metadata TTL for item {metadata_item.id}")

            # Check for certificates in building metadata
            if "Building" in metadata_item.classType:
                await self._process_building_certificates(usecase_name, metadata_item)

        except Exception as e:
            logger.error(f"Error processing metadata item: {e}")

    async def _process_building_certificates(self, usecase_name: str, metadata_item: GraphMetadataItem):
        """Process certificates found in building metadata."""
        try:
            certificates = metadata_item.propertiesValues.get("Certificates", [])

            for cert_data in certificates:
                if isinstance(cert_data, dict) and "pdf-path" in cert_data:
                    certificate = Certificate(**cert_data)
                    await self._process_certificate_document(usecase_name, metadata_item.id, certificate.pdf_path)

        except Exception as e:
            logger.error(f"Error processing building certificates: {e}")

    async def _process_certificate_document(self, usecase_name: str, building_id: str, pdf_path: str):
        """Process a certificate document from MinIO."""
        try:
            logger.info(f"Processing certificate document: {pdf_path}")

            # Check if document exists in MinIO
            if not self.deps.minio_client:
                logger.warning("MinIO client not available")
                return

            try:
                # Download the document from MinIO
                file_data = await self.deps.minio_client.download_data(pdf_path)
                if not file_data:
                    logger.warning(f"Document not found in MinIO: {pdf_path}")
                    return

                logger.info(f"Found document in MinIO: {pdf_path}")

                # Process with Docling if available
                if self.deps.docling_service:
                    docling_result = await self.deps.docling_service.process_document_from_bytes(
                        file_data=file_data,
                        filename=pdf_path.split("/")[-1]  # Extract filename from path
                    )

                    if docling_result.success:
                        logger.info(f"Successfully processed document with Docling")

                        # Save processed content back to MinIO
                        await self._save_processed_certificate_content(pdf_path, docling_result)

                        # Add to vector database for search
                        content = docling_result.markdown_content or str(docling_result.json_content)
                        if content:
                            # Prepare file paths for all generated files
                            base_path = pdf_path.rsplit(".", 1)[0]  # Remove extension
                            file_paths = {
                                "original_pdf": pdf_path,
                                "markdown": f"{base_path}.md" if docling_result.markdown_content else None,
                                "json": f"{base_path}.json" if docling_result.json_content else None
                            }
                            # Remove None values
                            file_paths = {k: v for k, v in file_paths.items() if v is not None}

                            await self._add_to_vector_db(
                                usecase_name=usecase_name,
                                content=content,
                                metadata={
                                    "document": {
                                        "type": "certificate",
                                        "original_path": pdf_path,
                                        "file_paths": file_paths,
                                        "building_id": building_id
                                    },
                                    "usecase": {
                                        "name": usecase_name,
                                        "collection": usecase_name.lower().replace(' ', '_').replace('-', '_')
                                    },
                                    "processing": {
                                        "method": "docling",
                                        "docling_metadata": docling_result.metadata,
                                        "content_type": "markdown" if docling_result.markdown_content else "json"
                                    }
                                }
                            )
                    else:
                        logger.error(f"Failed to process document: {docling_result.error_message}")
                else:
                    logger.warning("Docling service not available")

            except Exception as e:
                logger.warning(f"Error accessing document in MinIO: {pdf_path} - {e}")

        except Exception as e:
            logger.error(f"Error processing certificate document: {e}")

    async def _save_processed_certificate_content(self, original_path: str, docling_result):
        """Save processed certificate content back to MinIO."""
        try:
            base_path = original_path.rsplit(".", 1)[0]  # Remove extension

            # Save JSON if available
            if docling_result.json_content:
                json_path = f"{base_path}.json"
                import json
                json_data = json.dumps(docling_result.json_content, indent=2).encode('utf-8')
                await self.deps.minio_client.upload_data(
                    data=json_data,
                    object_name=json_path,
                    content_type="application/json"
                )
                logger.info(f"Saved certificate JSON content: {json_path}")

            # Save Markdown if available
            if docling_result.markdown_content:
                md_path = f"{base_path}.md"
                md_data = docling_result.markdown_content.encode('utf-8')
                await self.deps.minio_client.upload_data(
                    data=md_data,
                    object_name=md_path,
                    content_type="text/markdown"
                )
                logger.info(f"Saved certificate Markdown content: {md_path}")

        except Exception as e:
            logger.error(f"Error saving processed certificate content: {e}")

    async def _save_processed_content(self, original_path: str, docling_result):
        """Save processed content back to MinIO."""
        try:
            base_path = original_path.rsplit(".", 1)[0]  # Remove extension
            
            # Save JSON if available
            if docling_result.json_content:
                json_path = f"{base_path}.json"
                import json
                json_data = json.dumps(docling_result.json_content, indent=2).encode('utf-8')
                await self.deps.minio_client.upload_data(
                    data=json_data,
                    object_name=json_path,
                    content_type="application/json"
                )
                logger.info(f"Saved JSON content: {json_path}")
            
            # Save Markdown if available
            if docling_result.markdown_content:
                md_path = f"{base_path}.md"
                md_data = docling_result.markdown_content.encode('utf-8')
                await self.deps.minio_client.upload_data(
                    data=md_data,
                    object_name=md_path,
                    content_type="text/markdown"
                )
                logger.info(f"Saved Markdown content: {md_path}")
                
        except Exception as e:
            logger.error(f"Error saving processed content: {e}")

    async def _add_to_vector_db(self, usecase_name: str, content: str, metadata: Dict[str, Any]):
        """Add content to vector database with proper chunking and collection management."""
        try:
            # Create or get collection for this usecase
            collection_name = await self.deps.qdrant_client.create_usecase_collection(usecase_name)

            # Use the RAG agent's embedding function
            async def get_embedding(text: str):
                return await self.rag_agent._get_embedding(text)

            # Upsert document chunks to the usecase-specific collection
            success = await self.deps.qdrant_client.upsert_document_chunks(
                collection_name=collection_name,
                document_content=content,
                document_metadata=metadata,
                embedding_function=get_embedding,
                chunk_size=1000,  # Configurable chunk size
                overlap=200       # Configurable overlap
            )

            if success:
                logger.info(f"Added document chunks to vector DB collection '{collection_name}' for usecase '{usecase_name}'")
            else:
                logger.warning(f"Failed to add document chunks to vector DB for usecase '{usecase_name}'")

        except Exception as e:
            logger.error(f"Error adding to vector DB: {e}")

    def _create_searchable_content(self, data) -> str:
        """Create searchable text content from data objects."""
        if isinstance(data, BuildingData):
            parts = []
            if data.name:
                parts.append(f"Building: {data.name}")
            if data.address:
                parts.append(f"Address: {data.address}")
            if data.coordinates:
                parts.append(f"Coordinates: {data.coordinates}")
            
            # Add properties
            for key, value in data.properties.items():
                parts.append(f"{key}: {value}")
            
            return " | ".join(parts)
            
        elif isinstance(data, AddressData):
            parts = []
            if data.street:
                parts.append(f"Street: {data.street}")
            if data.city:
                parts.append(f"City: {data.city}")
            if data.postal_code:
                parts.append(f"Postal Code: {data.postal_code}")
            if data.country:
                parts.append(f"Country: {data.country}")
            if data.coordinates:
                parts.append(f"Coordinates: {data.coordinates}")
            
            # Add properties
            for key, value in data.properties.items():
                parts.append(f"{key}: {value}")
            
            return " | ".join(parts)
        
        return str(data)
