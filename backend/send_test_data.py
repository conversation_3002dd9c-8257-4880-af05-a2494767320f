#!/usr/bin/env python3
"""
Send test data to Kafka using the example input file.
"""
import asyncio
import json
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def send_test_data():
    """Send test data to Kafka."""
    try:
        from infrastructure.kafka_client import KafkaClient
        from config.settings import Settings
        
        logger.info("Sending test data to Kafka...")
        
        # Initialize Kafka client
        settings = Settings()
        kafka_client = KafkaClient(settings.kafka)
        await kafka_client.initialize()
        
        # Load example data
        example_file = Path("../.context/example_files/example_input.json")
        if not example_file.exists():
            logger.error("Example data file not found")
            return False
        
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        logger.info(f"Loaded {len(example_data)} example items")
        
        # Send first 5 items to test
        topic = "usecases.PortfolioExample"
        success_count = 0
        
        for i, item in enumerate(example_data[:5]):
            try:
                logger.info(f"Sending item {i+1}: {item.get('useCase', 'unknown')}")
                await kafka_client.produce_message(topic, item)
                success_count += 1
                logger.info(f"✓ Item {i+1} sent successfully")
                
                # Small delay between messages
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"✗ Failed to send item {i+1}: {e}")
        
        await kafka_client.close()
        
        logger.info(f"✓ Test data sending completed: {success_count}/5 messages sent")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"Test data sending failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(send_test_data())
    exit(0 if success else 1)
